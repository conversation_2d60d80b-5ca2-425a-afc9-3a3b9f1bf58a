# 多条公告数据结构设计

## 向后兼容的数据结构

### 数据库结构 (systemSettings 集合)

```javascript
{
  _id: "system_settings",
  contact: {
    phone: "138-8888-8888",
    address: "门店地址",
    
    // 保留原字段，确保旧版本兼容
    announcement: "第一条公告内容",  
    
    // 新增多条公告数组
    announcements: [
      {
        id: "uuid-1",                    // 唯一标识
        content: "第一条公告内容",        // 公告内容
        createTime: "2024-01-01T00:00:00Z", // 创建时间
        updateTime: "2024-01-01T00:00:00Z", // 更新时间
        order: 1,                        // 显示顺序
        isActive: true                   // 是否启用
      },
      {
        id: "uuid-2",
        content: "第二条公告内容",
        createTime: "2024-01-02T00:00:00Z",
        updateTime: "2024-01-02T00:00:00Z", 
        order: 2,
        isActive: true
      }
    ]
  }
}
```

## 兼容性策略

### 1. 数据同步规则
- 当 `announcements` 数组存在且有内容时：
  - `announcement` 字段自动同步为第一条启用公告的内容
  - 确保旧版本始终能读取到公告
- 当只有 `announcement` 字段时：
  - 新版本将其作为单条公告显示
  - 可以升级为多条公告格式

### 2. 读取优先级
- 新版本：优先读取 `announcements` 数组，如果为空则读取 `announcement`
- 旧版本：继续读取 `announcement` 字段，不受影响

### 3. 写入策略
- 新版本写入时：
  - 同时更新 `announcements` 数组和 `announcement` 字段
  - `announcement` = 第一条启用公告的内容
- 旧版本写入时：
  - 只更新 `announcement` 字段
  - 不影响 `announcements` 数组

## 前端显示逻辑

### 首页显示规则
```javascript
// 获取有效公告列表
const activeAnnouncements = announcements?.filter(item => item.isActive) || [];

if (activeAnnouncements.length === 0) {
  // 无公告：不显示公告区域
} else if (activeAnnouncements.length === 1) {
  // 单条公告：静态显示
} else {
  // 多条公告：使用 swiper 滚动显示
}
```

### 编辑界面逻辑
- 使用 SwipeCell 组件显示每条公告
- 支持拖拽排序（更新 order 字段）
- 滑动显示编辑、删除按钮
- 新增按钮创建新公告

## 云函数更新要点

### getSystemSettings 增强
```javascript
// 返回完整数据，包含兼容字段
return {
  success: true,
  data: {
    contact: {
      phone: settings.contact?.phone || '',
      address: settings.contact?.address || '',
      announcement: settings.contact?.announcement || '', // 兼容字段
      announcements: settings.contact?.announcements || [] // 新字段
    }
  }
}
```

### updateSystemSettings 增强
```javascript
// 处理多条公告更新
if (contact.announcements) {
  // 同步第一条公告到 announcement 字段
  const firstActive = contact.announcements.find(item => item.isActive);
  contact.announcement = firstActive?.content || '';
}
```

## 迁移策略

### 数据迁移
1. 检查现有 `announcement` 字段
2. 如果存在且 `announcements` 为空，则创建迁移：
```javascript
announcements: [{
  id: generateUUID(),
  content: announcement,
  createTime: new Date(),
  updateTime: new Date(),
  order: 1,
  isActive: true
}]
```

### 版本兼容
- 新版本发布后，逐步迁移用户数据
- 保持 `announcement` 字段始终有值
- 旧版本用户升级后自动享受多条公告功能
